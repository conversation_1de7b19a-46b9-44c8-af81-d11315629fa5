=== Embed Files from Google Drive ===
Contributors: slaFFik, jaredatch, smub
Tags: google, document, google apps, google drive, embed
Requires at least: 5.5
Requires PHP: 7.2
Tested up to: 6.8
Stable tag: 5.3.0
License: GPL-2.0-or-later

Browse for Google Drive documents and embed directly in your posts/pages. Extends Google Apps Login plugin so no extra user auth required.

== Description ==

Embedder For Google Drive gives authors easy access to their Google Drive in a popup box, where they can select documents to embed directly into their post or page. Just as easily as picking a photo from the WordPress media gallery.

Documents can be embedded in-line, using Google's read-only interactive viewer for your audience to read them. Documents can also be placed in your site as links to open up editable files (either in the same or a new browser tab), or downloaded straight to the computer.

You will need to set document sharing settings to ensure your website viewers have access to view your documents (e.g. at least 'anyone with the link can view' to be visible to all visitors).

**This plugin requires that you also install the free (or premium/enterprise) version of the popular [Google Apps Login](https://wordpress.org/plugins/google-apps-login/) plugin**

Displays many file types such as PDF, Word DOC, ZIP, Videos, Images etc plus native Google docs (Spreadsheet, Drawing, Forms, etc). Step through a list of your Drive files or search to find the one you're looking for.

Choose from:

*  Viewer file link - full page Google viewer and editor
*  Download file link - directly download file to your computer (disabled for native Google doc formats)
*  Embed document - display most file types inline in your posts or pages (non-Google file types need sharing settings at least 'anyone with the link can view')

Useful for public websites or private intranets (all visitors should be able to view files as long as sharing settings are 'anyone with the link can view' or higher in Google Drive).
Works on all WordPress installations including multisite networks.

Please note that to embed folders you will require the Premium or Enterprise version of this plugin.

[youtube https://www.youtube.com/watch?v=wcpjcFJIOko]

= Paid versions and Support =

You can also purchase the premium or enterprise versions of Embedder For Google Drive, including support.

The **Premium version** contains some important extra features:

* Browse My Drive, Recent Files, Starred, and Shared With Me tabs to locate Drive files the way you're used to.
* Embed Folders: simply keep your Google Drive folder up-to-date with your files, and your staff or website visitors will always be able to view a list of the latest documents.
* Google Calendars: pick from your Google Calendars and provide download links to ICAL or XML, or embed them directly in your site.
* Support and updates for one year.

[Premium - See details and purchase](https://wp-glogin.com/drive/?utm_source=Drive%20ReadmePromo&utm_medium=freemium&utm_campaign=Freemium)

The **Enterprise version** integrates Google Drive much more closely with your WordPress intranet, essentially allowing each
page or post on your intranet to host its own file attachments, completely backed by Drive.

This means you no longer need to manage Drive and your Intranet as two completely separate document sharing systems!

It also introduces advanced *interactive embedded folders*, where users can preview files and drill-down into subfolders all without
leaving your site.

It also contains all the features of the basic and premium versions, plus support for *Team Drives (Shared Drives)*.

[Enterprise - Find out more](https://wp-glogin.com/drive/enterprise/?utm_source=Drive%20ReadmeEntPromo&utm_medium=freemium&utm_campaign=Freemium)

= Requirements =

Google Drive document embedding and one-click login will work for the following domains and user accounts:

*  G Suite Basic (Google Apps for Work)
*  G Suite Business (Google Apps Unlimited for Work)
*  G Suite for Education (Google Apps for Education)
*  G Suite for Non-profits (Google Apps for Non-profits)
*  G Suite for Government (Google Apps for Government)
*  Personal gmail.com and googlemail.com emails

Google Apps Login plugin setup requires you to have admin access to any G Suite domain (formerly Google Apps), or a regular Gmail account, to register and
obtain two simple codes from Google.

= Google Apps Login =

The [Google Apps Login](https://wordpress.org/plugins/google-apps-login/) plugin (which you must also install)
allows existing WordPress user accounts to log in to the website
using Google to securely authenticate their account. This means that if they are already logged into Gmail for example,
they can simply click their way through the WordPress login screen - no username or password is explicitly required!

Full support and premium features are also available for purchase:

Eliminate the need for G Suite / Google Apps domain admins to  separately manage WordPress user accounts, and get peace
of mind that only authorized employees have access to the organizations's websites and intranet.

**See [https://wp-glogin.com/](https://wp-glogin.com/?utm_source=Drive%20Readme&utm_medium=freemium&utm_campaign=Freemium)**


== Screenshots ==

1. Insert Drive File button is added to post/page admin screen
2. Browse for your Google Drive files and select how to embed
3. Read-only documents can be embedded in the browser...
4. ...or links to open editable documents in a new tab

== Frequently Asked Questions ==

= How can I obtain support for this product? =

Full support is available if you purchase the premium or enterprise license from the author via:
[https://wp-glogin.com/drive/](https://wp-glogin.com/drive/?utm_source=Drive%20Readme%20Premium&utm_medium=freemium&utm_campaign=Freemium)

The paid plugins also support more file types (such as embedded Videos and Drive Folders) and allows you to
browse your Google Calendars to embed in your posts/pages.

Please feel free to email [<EMAIL>](mailto:<EMAIL>) with any questions (specifying Drive in the subject),
as we may be able to help, but you may be required to purchase a support license if the problem
is specific to your installation or requirements.

We may occasionally be able to respond to support queries posted on the 'Support' forum here on the wordpress.org plugin page, but we recommend sending us an email instead if possible.

= How can I embed Drive Folders in my website? =

You will need to purchase either the premium or enterprise version of Embedder For Google Drive from
[our website](https://wp-glogin.com/drive/?utm_source=Drive%20Readme%20FoldersWebsite&utm_medium=freemium&utm_campaign=Freemium).

The Premium version will allow you to embed folders in a basic 'iframe' format.

For advanced 'interactive' folders, where users can preview files and drill-down into subfolders all without leaving your site,
please take a look at the
[Enterprise version](https://wp-glogin.com/drive/enterprise/?utm_source=Drive%20Readme%20FoldersEnterprise&utm_medium=freemium&utm_campaign=Freemium).

The Enterprise version of Embedder For Google Drive integrates Drive much more closely with your WordPress intranet,
essentially allowing each page or post on your intranet to host its own file attachments, completely backed by Drive.

This means you no longer need to manage Drive and your Intranet as two completely separate document sharing systems!

[Find out more](https://wp-glogin.com/drive/enterprise/?utm_source=Drive%20Readme%20FoldersEnterprise&utm_medium=freemium&utm_campaign=Freemium).

= Why is the option for Viewer / Download / Embed disabled for some files? =

Download isn't normally enabled for native Google file types.

Embed should be enabled for many non-native file types (e.g. PDF, Word DOC). If not, you may need to increase sharing
settings within Google Drive to 'anyone with the link may view', or higher.

If that still doesn't work, your file type may not be supported. Please get in touch (send your file or share with
us if possible), and we will see if it can be supported - email <EMAIL>.

To embed Google Drive Folders or image file types, you will require the premium or enterprise version of the plugin. You will be
notified if that is the case for your selected file.

= How does the plugin respect Google Drive sharing settings? =

Embedder For Google Drive will show different behavior depending on your document's type and its sharing settings within Google Drive.

Generally, we recommend setting files' Sharing settings to at least 'anyone with the link can view', in order for them to be
visible to all visitors.

This setting is essential for third-party file types such as Word and PDF (otherwise all visitors will see unintelligible content).

You can use lower sharing settings for Google documents (e.g. share only within the organization, or with specific users), but in
that case you will need your users to be logged in to a Google account that is authorized to view the content (otherwise, they
will be told they do not have permission).

When using 'anyone with the link can view', you must understand that any visitors to your WordPress site will be able to obtain
that document's link and potentially open the document outside of your WordPress site.

= I embed a (non-Google) document, but I just see some HTML in the published post =

Specifically, you see something starting:

&lt;!DOCTYPE html&gt;
&lt;html...
&lt;head...

Most likely, you need to increase sharing settings for the file. In your Google Drive, find the file and click
'Share'. Click 'Change', and then on 'Anyone with the link can view'. Click 'Done'.

You also need to make sure you have not chosen to 'Prevent viewers from downloading'. To check this, find the file in
your Google Drive list, and see if 'Allow viewers to download' is available from the right-click menu. If so, click it
to turn it off!

If you reload your published page or post in WordPress, it should now display properly, or at least give a message
saying that the document type is unsupported. Please get in touch if not!

= In the Add Google File dialog box, I just get the Google Error "Forbidden". Even though I promise I followed the instructions, including enabling Drive API! =

Google Apps' user and permissions systems are complex, and there are many ways you could forbid your users from using the Drive API.

You could have disabled API access completely, so take a look in your GA admin panel and look for anything obvious.

One reason a lot have people have seen 'forbidden' in the past is because they have the following setting unchecked:
go to Google Apps -> Drive, and then General Settings -> Allow users to install Google Drive apps.

After changing settings, you should logout of WordPress and Google, then refresh and try all over again.

= How is this different to the plugin Google Doc Embedder? =

Google Doc Embedder only allows you to embed other files such as PDF, Word etc in your site. It has nothing much to do
with Google, other than the fact it uses an online Google service to render documents.
By contrast, our plugin (Embedder For Google Drive) allows you to browse your Google Drive files and easily
embed those directly into your site - both native Google formats and other file types PDF, Word, ZIP etc.

= Why do I also need to install the Google Apps Login plugin? =

This Google Drive plugin extends the Google Apps Login plugin, making use of that plugin's settings rather than
insisting that you register a new whole new application with Google for each plugin separately. For those of your users
who choose to Login via Google to connect to your WordPress site, they only need to authenticate once to be able to browse
their Google Drive through the Google Drive plugin, rather than having to click a second time to allow Drive access.

Because of this, the Google Drive plugin itself requires no configuration at all - it is delegated to Google Apps Login.

For Multisite Network, this means that admins can set up Google Apps Login network-wide, but safely defer the choice of activating
any further plugins (which extend Google Apps Login) to individual site administrators.

= I have installed Google Apps Login plugin but the Google Drive plugin still says I need to install it =

Are you sure you have upgraded to the latest version?

Have you also configured the plugin? Under Settings -> Google Apps Login, you will need to follow the instructions
to obtain a Client ID and Client Secret from Google Cloud Console, and enter them into that settings page.

= How can I purchase the premium or enterprise version? =

You can purchase a license here:
[https://wp-glogin.com/drive/](https://wp-glogin.com/drive/?utm_source=Drive%20ReadmeFAQ&utm_medium=freemium&utm_campaign=Freemium)

== Installation ==

For the Google Drive plugin to work, you will need also need to install and configure the Google Apps Login plugin (either before or after).

Google Drive plugin:

1. Go to your WordPress admin control panel's plugin page
1. Search for "Embedder For Google Drive" or "Embedder for Google Drive"
1. Click Install
1. Click Activate on the plugin
1. If you do not have the correct version of Google Apps Login installed, you will see a warning notice to that effect, in which case you should follow the instructions below

Google Apps Login plugin:

1. Go to your WordPress admin control panel's plugin page
1. Search for "Google Apps Login" or "Login for Google Apps"
1. Click Install
1. Click Activate on the plugin
1. Go to 'Google Apps Login' under Settings in your WordPress admin area
1. Follow the instructions on that page to obtain two codes from Google, and also submit two URLs back to Google
1. **In the Google Cloud Console, you must also enable the switch for Google Drive API access**

If you cannot install from the WordPress plugins directory for any reason, and need to install from ZIP file:

1. For Google Drive plugin: Upload `google-drive-embedder` directory and contents to the `/wp-content/plugins/` directory, or upload the ZIP file directly in the Plugins section of your WordPress admin
1. For Google Apps Login plugin: Upload `google-apps-login` directory and contents to the `/wp-content/plugins/` directory, or upload the ZIP file directly in the Plugins section of your WordPress admin
1. Follow the instructions to configure the Google Apps Login plugin post-installation

== Changelog ==

= 5.3.0 =
* Updated: Compatibility with WordPress 6.8.
* Updated: Compatibility with PHP 8.
* Fixed: A lot of plugin strings were not translatable, now the plugin can be fully translated into any language.
* Fixed: Images from Google Drive can now again be embedded on a page. Don't forget to set the proper width and height in a shortcode or a block.
* Fixed: A lot of code styles fixes and cleanups.
* Fixed: In certain cases, the plugin was generating a lot of PHP Notices and Deprecation notices on PHP 8.

= 5.2.6 =
* Updated: Move to Google Identity Services.

= 5.2.5 =
* Fix: PHP Error with Shortcode.

= 5.2.4 =
* Change: Update Plugin Name.
* Fix: Sanitize setting inputs.

= 5.2.3 =
* Added compatibility for WordPress 5.6.

= 5.2.2 =
* Updated Team Drive to Shared Drive in compliance of Google Drive updates.
* Added compatibility for WordPress 5.4.1.

= 5.2.1 =
* Added American with Disabilities Act compliance compatibility for Embedder For Google Drive folder and files icons.
* Added compatibility for WordPress 5.3.2.

= 5.2 =
* Added compatibility for WordPress 5.3.1.

= 5.1 =
* Added compatibility for WordPress 5.2.1.

= 5.0 =
* Added compatibility for WordPress 5.0.

= 4.2 =
* Compatibility for Gutenberg Editor.

= 4.1 =
* No longer asks user for Drive permissions when they 'Login with Google'. Instead, it asks for Drive permissions only when they come to interact with the Drive functionality (clicking 'Add Google File' for example).

= 4.0 =
* Free version now supports videos, images, and forms.
* Changed to Google Drive API v3.
